import 'package:floating_frosted_bottom_bar/app/frosted_bottom_bar.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_social/cubit/post/home/<USER>';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/explorer/explorer_screen.dart';
import 'package:toii_social/screen/home/<USER>';
import 'package:toii_social/screen/profile/profile_screen.dart';
import 'package:toii_social/screen/wallet/wallet_screen.dart';
import 'package:toii_social/widget/colors/colors.dart';

class MainTabbarScreen extends StatefulWidget {
  const MainTabbarScreen({super.key});

  @override
  State<MainTabbarScreen> createState() => _MainTabbarScreenState();
}

class _MainTabbarScreenState extends State<MainTabbarScreen>
    with SingleTickerProviderStateMixin {
  late int currentPage;
  late TabController tabController;
  final _homeKey = GlobalKey<HomeMainPageState>();
  @override
  void initState() {
    currentPage = 0;
    tabController = TabController(
      length: 5,
      vsync: this,
      animationDuration: const Duration(milliseconds: 300), // Smooth animation
    );
    tabController.addListener(() {
      if (!tabController.indexIsChanging) {
        final value = tabController.index;
        if (value != currentPage && mounted) {
          changePage(value);
        }
      }
    });

    super.initState();
  }

  void changePage(int newPage) async {
    if (newPage == currentPage && newPage == 0) {
      _homeKey.currentState?.scrollToTop();
      return;
    }
    if (newPage != currentPage && mounted) {
      if (newPage == 2) {
        final reusult = await context.push(RouterEnums.createPost.routeName);
        if (reusult != null && reusult is PostModel) {
          GetIt.instance<HomeCubit>().getUserFeed(isRefresh: true);
        }
        if (mounted) {
          setState(() {
            currentPage = 0;
            tabController.animateTo(0); // Use animateTo for smoother transition
          });
        }
        return;
      }
      if (mounted) {
        setState(() {
          currentPage = newPage;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => GetIt.instance<ProfileCubit>()..getProfile(),

      child: Scaffold(
        body: FrostedBottomBar(
          bottom: MediaQuery.of(context).padding.bottom,
          opacity: 0.85, // Slightly higher opacity for better performance
          sigmaX: 3, // Reduced blur for better performance
          sigmaY: 3, // Reduced blur for better performance
          borderRadius: BorderRadius.circular(500),
          duration: const Duration(
            milliseconds: 250,
          ), // Slightly longer for smoother animation
          hideOnScroll: true,
          body:
              (context, controller) => TabBarView(
                controller: tabController,
                dragStartBehavior: DragStartBehavior.start,
                physics:
                    const ClampingScrollPhysics(), // Better for smooth swiping
                clipBehavior: Clip.none, // Prevent clipping during animation
                children: [
                  HomeMainPage(scrollController: controller, key: _homeKey),
                  const ExplorerScreen(),
                  Container(color: Colors.white), // Placeholder for add tab
                  WalletScreen(),
                  ProfileScreen(),
                ],
              ),
          child: TabBar(
            // onTap: (index) {
            //   changePage(index);
            // },
            indicatorPadding: const EdgeInsets.fromLTRB(6, 0, 6, 0),
            controller: tabController,
            // indicator: CircleTabIndicator(color: themeData.primaryGreen500),
            tabs: [
              TabsIcon(
                icon:
                    currentPage == 0
                        ? SvgPicture.asset(
                          Assets.icons.icTabbarHomeActive.path,
                          height: 60,
                          fit: BoxFit.fitHeight,
                        )
                        : Assets.icons.icHomeTest.svg(
                          fit: BoxFit.fitHeight,
                          height: 60,
                        ),
              ),
              TabsIcon(
                icon: SvgPicture.asset(
                  currentPage == 1
                      ? Assets.icons.icTabbarExploreActive.path
                      : Assets.icons.icTabbarExplore.path,
                  height: 60,
                  fit: BoxFit.fitHeight,
                ),
              ),

              TabsIcon(
                icon: SvgPicture.asset(
                  Assets.icons.icTabbarPost.path,
                  height: 60,
                  fit: BoxFit.fitHeight,
                ),
              ),
              TabsIcon(
                icon: SvgPicture.asset(
                  currentPage == 3
                      ? Assets.icons.icTabbarWalletActive.path
                      : Assets.icons.icTabbarWallet.path,
                  height: 60,
                  fit: BoxFit.fitHeight,
                ),
              ),
              TabsIcon(
                icon: SvgPicture.asset(
                  currentPage == 4
                      ? Assets.icons.icTabbarProfileActive.path
                      : Assets.icons.icTabbarProfile.path,
                  height: 60,
                  fit: BoxFit.fitHeight,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class TabsIcon extends StatelessWidget {
  final Widget icon;

  const TabsIcon({super.key, required this.icon});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 72,
      height: 64,
      child: Center(
        child: RepaintBoundary(
          child:
              icon, // Wrap icon in RepaintBoundary to prevent unnecessary repaints
        ),
      ),
    );
  }
}

class CircleTabIndicator extends Decoration {
  final BoxPainter _painter;

  CircleTabIndicator({required Color color})
    : _painter = _CirclePainter(color, 3);

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) => _painter;
}

class _CirclePainter extends BoxPainter {
  final Paint _paint;
  final double radius;

  _CirclePainter(Color color, this.radius)
    : _paint =
          Paint()
            ..color = themeData.primaryGreen500
            ..isAntiAlias = true;

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration cfg) {
    final Offset circleOffset =
        offset + Offset(cfg.size!.width / 2, cfg.size!.height - radius - 5);
    canvas.drawCircle(circleOffset, radius, _paint);
  }
}
