import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/cubit/post/home/<USER>';
import 'package:toii_social/cubit/post/like_post/like_post_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/screen/home/<USER>/post/post_icon_text.dart';

class PostLikeButton extends StatefulWidget {
  final bool initialLiked;
  final PostModel post;
  const PostLikeButton({
    super.key,
    required this.post,
    required this.initialLiked,
  });

  @override
  State<PostLikeButton> createState() => _PostLikeButtonState();
}

class _PostLikeButtonState extends State<PostLikeButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scale;
  bool _previousLiked = false;

  @override
  void initState() {
    super.initState();
    _previousLiked = widget.initialLiked;
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _scale = Tween<double>(
      begin: 1.0,
      end: 1.4,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.elasticOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext _) {
    return BlocProvider(
      create:
          (context) => LikePostCubit(
            postId: widget.post.id,
            post: widget.post,
            initialLiked: widget.initialLiked,
          ),
      child: BlocListener<LikePostCubit, LikePostState>(
        listener: (context, state) {
          // Update HomeCubit when like state changes
          if (state.status == LikePostStatus.success) {
            final homeCubit = GetIt.instance<HomeCubit>();
            homeCubit.updatePostLikeCount(
              widget.post.id,
              state.likeCount,
              state.isLiked,
            );
          }
        },
        child: BlocBuilder<LikePostCubit, LikePostState>(
          builder: (context, state) {
            // Trigger animation when like state changes
            if (state.isLiked != _previousLiked &&
                _controller.status == AnimationStatus.dismissed) {
              _previousLiked = state.isLiked;
              _controller.forward().then((_) => _controller.reverse());
            }

            // Format like count for display
            String likeCountText;
            if (state.likeCount == 0) {
              likeCountText = "";
            } else if (state.likeCount >= 1000) {
              likeCountText = "${(state.likeCount / 1000).toStringAsFixed(1)}K";
            } else {
              likeCountText = state.likeCount.toString();
            }

            return PostIconText(
              icon: ScaleTransition(
                scale: _scale,
                child:
                    state.isLiked
                        ? Assets.icons.icHeartFilled.svg(
                          width: 28,
                          height: 28,
                          color: Colors.lightBlueAccent,
                        )
                        : Assets.icons.icLike1.image(width: 24, height: 24),
              ),
              text: likeCountText,
              onTap: () {
                // Trigger like/unlike immediately
                context.read<LikePostCubit>().toggleLike();
              },
            );
          },
        ),
      ),
    );
  }
}
