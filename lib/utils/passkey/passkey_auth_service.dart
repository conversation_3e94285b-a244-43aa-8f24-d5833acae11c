import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:get_it/get_it.dart';
import 'package:passkeys/passkeys.dart';
import 'package:toii_social/core/repository/auth_repository.dart';
import 'package:toii_social/model/auth/attestation_nonce/attestation_nonce_model.dart';
import 'package:toii_social/utils/keychain/keychain_service.dart';

/// Service for handling passkey authentication operations
/// Integrates with Android's credential_manager for cross-device sync
class PasskeyAuthService {
  late final PasskeyAuthenticator _authenticator;
  late final AuthRepository _authRepository;
  late final KeychainService _keychainService;

  static final PasskeyAuthService _instance = PasskeyAuthService._internal();
  factory PasskeyAuthService() => _instance;

  PasskeyAuthService._internal() {
    _authenticator = PasskeyAuthenticator(debugMode: true);
    _authRepository = GetIt.instance<AuthRepository>();
    _keychainService = KeychainService.instance;
  }

  /// Check if passkeys are supported on the current device
  Future<bool> isPasskeySupported() async {
    try {
      if (Platform.isAndroid) {
        final availability = await _authenticator.getAvailability().android();
        return availability.hasPasskeySupport;
      } else if (Platform.isIOS) {
        final availability = await _authenticator.getAvailability().ios();
        return availability.hasPasskeySupport;
      }
      return false;
    } catch (e) {
      log('Error checking passkey support: $e');
      return false;
    }
  }

  /// Check if user has existing passkey credentials
  Future<bool> hasExistingPasskey() async {
    try {
      final credentialId = await _keychainService.getPasskeyCredentialId();
      return credentialId != null && credentialId.isNotEmpty;
    } catch (e) {
      log('Error checking existing passkey: $e');
      return false;
    }
  }

  /// Register a new passkey for the user
  /// Should be called after successful account creation
  Future<PasskeyRegistrationResult> registerPasskey({
    required String userEmail,
    required String userName,
    required String walletAddress,
  }) async {
    try {
      // Check if passkeys are supported
      if (!await isPasskeySupported()) {
        return PasskeyRegistrationResult.notSupported();
      }

      // Get attestation nonce from backend
      final nonceResponse = await _authRepository.getAttestationNonce();
      final nonce = nonceResponse.data.nonce;

      // Create WebAuthn challenge for registration
      final challenge = _createRegistrationChallenge(
        nonce: nonce,
        userEmail: userEmail,
        userName: userName,
      );

      // Register passkey with platform authenticator
      final registrationResult = await _authenticator.register(challenge);

      // Send attestation data to backend
      final attestationRequest = AttestationRegisterRequestModel(
        attestationData: registrationResult.response.attestationObject,
        keyId: registrationResult.response.id,
        networkBase: "ethereum",
        walletAddress: walletAddress,
        walletProvider: "passkey",
      );

      final loginResponse = await _authRepository.registerAttestation(attestationRequest);

      // Store passkey metadata locally
      await _keychainService.savePasskeyCredentials(
        credentialId: registrationResult.response.id,
        userHandle: base64Encode(utf8.encode(userEmail)),
        userEmail: userEmail,
      );

      return PasskeyRegistrationResult.success(
        accessToken: loginResponse.data.accessToken,
        credentialId: registrationResult.response.id,
      );
    } catch (e) {
      log('Error registering passkey: $e');
      return PasskeyRegistrationResult.error(e.toString());
    }
  }

  /// Authenticate user with existing passkey
  /// This is called on app startup to restore user session
  Future<PasskeyAuthenticationResult> authenticateWithPasskey() async {
    try {
      // Check if passkeys are supported
      if (!await isPasskeySupported()) {
        return PasskeyAuthenticationResult.notSupported();
      }

      // Check if user has existing passkey
      if (!await hasExistingPasskey()) {
        return PasskeyAuthenticationResult.noCredentials();
      }

      // Get stored passkey metadata
      final credentialId = await _keychainService.getPasskeyCredentialId();
      final userEmail = await _keychainService.getPasskeyUserEmail();

      if (credentialId == null || userEmail == null) {
        return PasskeyAuthenticationResult.noCredentials();
      }

      // Get attestation nonce for authentication
      final nonceResponse = await _authRepository.getAttestationNonce();
      final nonce = nonceResponse.data.nonce;

      // Create WebAuthn challenge for authentication
      final challenge = _createAuthenticationChallenge(
        nonce: nonce,
        credentialId: credentialId,
      );

      // Authenticate with platform authenticator
      final authResult = await _authenticator.authenticate(challenge);

      // TODO: Send authentication result to backend for verification
      // For now, we'll assume success if the platform authentication succeeds
      // In a real implementation, you'd send the signed challenge to your backend

      return PasskeyAuthenticationResult.success(
        userEmail: userEmail,
        credentialId: authResult.response.id,
      );
    } catch (e) {
      log('Error authenticating with passkey: $e');
      return PasskeyAuthenticationResult.error(e.toString());
    }
  }

  /// Create WebAuthn challenge for passkey registration
  String _createRegistrationChallenge({
    required String nonce,
    required String userEmail,
    required String userName,
  }) {
    final challenge = {
      "publicKey": {
        "challenge": nonce,
        "rp": {
          "name": "Toii Social",
          "id": "toii.social", // This should match your domain
        },
        "user": {
          "id": base64Encode(utf8.encode(userEmail)),
          "name": userEmail,
          "displayName": userName,
        },
        "pubKeyCredParams": [
          {"alg": -7, "type": "public-key"}, // ES256
          {"alg": -257, "type": "public-key"}, // RS256
        ],
        "authenticatorSelection": {
          "authenticatorAttachment": "platform",
          "userVerification": "required",
          "residentKey": "required",
        },
        "timeout": 60000,
        "attestation": "direct",
      }
    };

    return jsonEncode(challenge);
  }

  /// Create WebAuthn challenge for passkey authentication
  String _createAuthenticationChallenge({
    required String nonce,
    required String credentialId,
  }) {
    final challenge = {
      "publicKey": {
        "challenge": nonce,
        "timeout": 60000,
        "rpId": "toii.social", // This should match your domain
        "allowCredentials": [
          {
            "id": credentialId,
            "type": "public-key",
            "transports": ["internal", "hybrid"],
          }
        ],
        "userVerification": "required",
      }
    };

    return jsonEncode(challenge);
  }

  /// Clear stored passkey credentials (for logout)
  Future<void> clearPasskeyCredentials() async {
    try {
      await _keychainService.clearPasskeyCredentials();
    } catch (e) {
      log('Error clearing passkey credentials: $e');
    }
  }
}

/// Result of passkey registration operation
class PasskeyRegistrationResult {
  final bool isSuccess;
  final bool isNotSupported;
  final String? accessToken;
  final String? credentialId;
  final String? error;

  PasskeyRegistrationResult._({
    required this.isSuccess,
    required this.isNotSupported,
    this.accessToken,
    this.credentialId,
    this.error,
  });

  factory PasskeyRegistrationResult.success({
    required String accessToken,
    required String credentialId,
  }) =>
      PasskeyRegistrationResult._(
        isSuccess: true,
        isNotSupported: false,
        accessToken: accessToken,
        credentialId: credentialId,
      );

  factory PasskeyRegistrationResult.notSupported() =>
      PasskeyRegistrationResult._(
        isSuccess: false,
        isNotSupported: true,
      );

  factory PasskeyRegistrationResult.error(String error) =>
      PasskeyRegistrationResult._(
        isSuccess: false,
        isNotSupported: false,
        error: error,
      );
}

/// Result of passkey authentication operation
class PasskeyAuthenticationResult {
  final bool isSuccess;
  final bool isNotSupported;
  final bool hasNoCredentials;
  final String? userEmail;
  final String? credentialId;
  final String? error;

  PasskeyAuthenticationResult._({
    required this.isSuccess,
    required this.isNotSupported,
    required this.hasNoCredentials,
    this.userEmail,
    this.credentialId,
    this.error,
  });

  factory PasskeyAuthenticationResult.success({
    required String userEmail,
    required String credentialId,
  }) =>
      PasskeyAuthenticationResult._(
        isSuccess: true,
        isNotSupported: false,
        hasNoCredentials: false,
        userEmail: userEmail,
        credentialId: credentialId,
      );

  factory PasskeyAuthenticationResult.notSupported() =>
      PasskeyAuthenticationResult._(
        isSuccess: false,
        isNotSupported: true,
        hasNoCredentials: false,
      );

  factory PasskeyAuthenticationResult.noCredentials() =>
      PasskeyAuthenticationResult._(
        isSuccess: false,
        isNotSupported: false,
        hasNoCredentials: true,
      );

  factory PasskeyAuthenticationResult.error(String error) =>
      PasskeyAuthenticationResult._(
        isSuccess: false,
        isNotSupported: false,
        hasNoCredentials: false,
        error: error,
      );
}
