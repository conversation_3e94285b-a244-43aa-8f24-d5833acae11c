import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:toii_social/model/credentials/credentials.dart';
import 'package:toii_social/model/user/user_model.dart';

class KeychainService {
  late final FlutterSecureStorage _storage;
  String isEnableBio = "key_Enable_Bio";
  String keyEmail = "key_Email";
  String keyPassword = "key_Password";
  String keyAddress = "key_Address";
  KeychainService._privateConstructor() {
    _storage = const FlutterSecureStorage(
      aOptions: AndroidOptions(encryptedSharedPreferences: true),
    );
  }

  static final KeychainService instance = KeychainService._privateConstructor();

  init() {
    _storage = const FlutterSecureStorage(
      aOptions: AndroidOptions(encryptedSharedPreferences: true),
    );
  }

  Future<String> getPasswordUser() async {
    var password = await _storage.read(key: keyPassword) ?? "";
    return password;
  }

  Future<String> getUserName() async {
    var email = await _storage.read(key: keyEmail) ?? "";
    return email;
  }

  void setUserName(String email) async {
    await _storage.write(key: keyEmail, value: email);
  }

  void setPassword(String password) async {
    await _storage.write(key: keyPassword, value: password);
  }

  void setEnableBio(bool value) async {
    await _storage.write(key: isEnableBio, value: value ? "1" : "0");
  }

  Future<bool> isEnableBiometric() async {
    var isEnable = await _storage.read(key: isEnableBio) ?? "";
    return int.tryParse(isEnable) == 1 ? true : false;
  }

  void clearAll() async {
    await _storage.deleteAll();
  }

  Future<void> savePrivateKeyToiCloud({
    required Credentials credentials,
  }) async {
    // Delete old wallet_data if exists
    if (Platform.isIOS) {
      _storage.delete(
        key: 'wallet_data',
        iOptions: const IOSOptions(
          accessibility: KeychainAccessibility.unlocked,
          synchronizable: true,
        ),
      );
    } else {
      _storage.delete(
        key: 'wallet_data',
        aOptions: const AndroidOptions(encryptedSharedPreferences: true),
      );
    }

    String? jsonData;

    // Platform-specific read
    if (Platform.isIOS) {
      jsonData = await _storage.read(
        key: 'wallet_login',
        iOptions: const IOSOptions(
          accessibility: KeychainAccessibility.unlocked,
          synchronizable: true, // ✅ Enables iCloud Keychain sync
        ),
      );
    } else {
      jsonData = await _storage.read(
        key: 'wallet_login',
        aOptions: const AndroidOptions(encryptedSharedPreferences: true),
      );
    }

    if (jsonData == null) {
      final map = {credentials.address: credentials.privateKeyHex};
      final jsonData = jsonEncode(map);

      // Platform-specific write
      if (Platform.isIOS) {
        await _storage.write(
          key: 'wallet_login',
          value: jsonData,
          iOptions: const IOSOptions(
            accessibility: KeychainAccessibility.unlocked,
            synchronizable: true, // ✅ Enables iCloud Keychain sync
          ),
        );
      } else {
        await _storage.write(
          key: 'wallet_login',
          value: jsonData,
          aOptions: const AndroidOptions(encryptedSharedPreferences: true),
        );
      }
    } else {
      final decoded = jsonDecode(jsonData);
      decoded[credentials.address] = credentials.privateKeyHex;
      final updatedJsonData = jsonEncode(decoded);

      // Platform-specific write
      if (Platform.isIOS) {
        await _storage.write(
          key: 'wallet_login',
          value: updatedJsonData,
          iOptions: const IOSOptions(
            accessibility: KeychainAccessibility.unlocked,
            synchronizable: true, // ✅ Enables iCloud Keychain sync
          ),
        );
      } else {
        await _storage.write(
          key: 'wallet_login',
          value: updatedJsonData,
          aOptions: const AndroidOptions(encryptedSharedPreferences: true),
        );
      }
    }
  }

  void setProfile(UserModel profile) async {
    if (profile.address == null) return;

    String? jsonData;

    // Platform-specific read
    if (Platform.isIOS) {
      jsonData = await _storage.read(
        key: 'wallet_profile',
        iOptions: const IOSOptions(
          accessibility: KeychainAccessibility.unlocked,
          synchronizable: true, // ✅ Enables iCloud Keychain sync
        ),
      );
    } else {
      jsonData = await _storage.read(
        key: 'wallet_profile',
        aOptions: const AndroidOptions(encryptedSharedPreferences: true),
      );
    }

    if (jsonData == null) {
      final map = {profile.walletAddress!: profile.toJson()};
      final jsonData = jsonEncode(map);

      // Platform-specific write
      if (Platform.isIOS) {
        await _storage.write(
          key: 'wallet_profile',
          value: jsonData,
          iOptions: const IOSOptions(
            accessibility: KeychainAccessibility.unlocked,
            synchronizable: true, // ✅ Enables iCloud Keychain sync
          ),
        );
      } else {
        await _storage.write(
          key: 'wallet_profile',
          value: jsonData,
          aOptions: const AndroidOptions(encryptedSharedPreferences: true),
        );
      }
    } else {
      final decoded = jsonDecode(jsonData);
      decoded[profile.walletAddress] = profile.toJson();
      final updatedJsonData = jsonEncode(decoded);

      // Platform-specific write
      if (Platform.isIOS) {
        await _storage.write(
          key: 'wallet_profile',
          value: updatedJsonData,
          iOptions: const IOSOptions(
            accessibility: KeychainAccessibility.unlocked,
            synchronizable: true, // ✅ Enables iCloud Keychain sync
          ),
        );
      } else {
        await _storage.write(
          key: 'wallet_profile',
          value: updatedJsonData,
          aOptions: const AndroidOptions(encryptedSharedPreferences: true),
        );
      }
    }
  }

  Future<List<String>> getListWallet() async {
    String? jsonData;

    // Platform-specific read
    if (Platform.isIOS) {
      jsonData = await _storage.read(
        key: 'wallet_login',
        iOptions: const IOSOptions(
          accessibility: KeychainAccessibility.unlocked,
          synchronizable: true, // ✅ Enables iCloud Keychain sync
        ),
      );
    } else {
      jsonData = await _storage.read(
        key: 'wallet_login',
        aOptions: const AndroidOptions(encryptedSharedPreferences: true),
      );
    }

    final Map<String, dynamic> decoded = jsonDecode(jsonData ?? "{}");
    final keys = decoded.keys.toList();
    return keys;
  }

  Future<String?> readPrivateKeyFromiCloud(String address) async {
    try {
      String? jsonData;

      // Platform-specific read
      if (Platform.isIOS) {
        jsonData = await _storage.read(
          key: 'wallet_login',
          iOptions: const IOSOptions(
            accessibility: KeychainAccessibility.unlocked,
            synchronizable: true, // ✅ Enables iCloud Keychain sync
          ),
        );
      } else {
        jsonData = await _storage.read(
          key: 'wallet_login',
          aOptions: const AndroidOptions(encryptedSharedPreferences: true),
        );
      }

      if (jsonData == null) return null;
      final decoded = jsonDecode(jsonData);

      return decoded[address] as String?;
    } catch (e) {
      log("Error reading private key: $e");
      return null;
    }
  }

  Future<Map<String, dynamic>> getProfile() async {
    String? jsonData;

    // Platform-specific read
    if (Platform.isIOS) {
      jsonData = await _storage.read(
        key: 'wallet_profile',
        iOptions: const IOSOptions(
          accessibility: KeychainAccessibility.unlocked,
          synchronizable: true, // ✅ Enables iCloud Keychain sync
        ),
      );
    } else {
      jsonData = await _storage.read(
        key: 'wallet_profile',
        aOptions: const AndroidOptions(encryptedSharedPreferences: true),
      );
    }

    final Map<String, dynamic> decoded = jsonDecode(jsonData ?? "{}");
    return decoded;
  }

  Future<bool> isFirstLaunch() async {
    // _storage.delete(
    //   key: 'wallet_login',
    //   iOptions: const IOSOptions(
    //     accessibility: KeychainAccessibility.unlocked, // or whenUnlocked
    //     synchronizable: true, // ✅ Enables iCloud Keychain sync
    //   ),
    // );
    //  _storage.delete(
    //   key: 'wallet_profile',
    //   iOptions: const IOSOptions(
    //     accessibility: KeychainAccessibility.unlocked, // or whenUnlocked
    //     synchronizable: true, // ✅ Enables iCloud Keychain sync
    //   ),
    // );

    String? jsonData;

    // Platform-specific read
    if (Platform.isIOS) {
      // iOS: Use iCloud Keychain sync
      jsonData = await _storage.read(
        key: 'wallet_login',
        iOptions: const IOSOptions(
          accessibility: KeychainAccessibility.unlocked,
          synchronizable: true, // ✅ Enables iCloud Keychain sync
        ),
      );
    } else {
      // Android: Use local storage only
      jsonData = await _storage.read(
        key: 'wallet_login',
        aOptions: const AndroidOptions(encryptedSharedPreferences: true),
      );
    }

    return (jsonData == null);
  }
}
