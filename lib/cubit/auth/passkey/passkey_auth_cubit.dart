import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/constant/key_shared.dart';
import 'package:toii_social/core/repository/auth_repository.dart';
import 'package:toii_social/utils/passkey/passkey_auth_service.dart';
import 'package:toii_social/utils/shared_prefs/shared_prefs.dart';

part 'passkey_auth_state.dart';

class PasskeyAuthCubit extends Cubit<PasskeyAuthState> {
  final PasskeyAuthService _passkeyAuthService;
  final AuthRepository _authRepository;

  PasskeyAuthCubit({
    PasskeyAuthService? passkeyAuthService,
    AuthRepository? authRepository,
  })  : _passkeyAuthService = passkeyAuthService ?? PasskeyAuthService(),
        _authRepository = authRepository ?? GetIt.instance<AuthRepository>(),
        super(const PasskeyAuthState());

  /// Check if passkeys are supported on this device
  Future<void> checkPasskeySupport() async {
    try {
      emit(state.copyWith(status: PasskeyAuthStatus.loading));
      
      final isSupported = await _passkeyAuthService.isPasskeySupported();
      
      emit(state.copyWith(
        status: PasskeyAuthStatus.initial,
        isSupported: isSupported,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: PasskeyAuthStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  /// Attempt to authenticate with existing passkey
  /// This is called on app startup
  Future<void> authenticateWithPasskey() async {
    try {
      emit(state.copyWith(status: PasskeyAuthStatus.authenticating));

      final result = await _passkeyAuthService.authenticateWithPasskey();

      if (result.isSuccess) {
        // Set authentication state
        await SharedPref.setBool(KeyShared.isLogin, true);
        // Note: We don't have an access token from passkey auth yet
        // This would need to be implemented with proper backend integration
        
        emit(state.copyWith(
          status: PasskeyAuthStatus.authenticated,
          userEmail: result.userEmail,
          credentialId: result.credentialId,
        ));
      } else if (result.isNotSupported) {
        emit(state.copyWith(
          status: PasskeyAuthStatus.notSupported,
          isSupported: false,
        ));
      } else if (result.hasNoCredentials) {
        emit(state.copyWith(
          status: PasskeyAuthStatus.noCredentials,
        ));
      } else {
        emit(state.copyWith(
          status: PasskeyAuthStatus.failure,
          errorMessage: result.error ?? 'Authentication failed',
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        status: PasskeyAuthStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  /// Register a new passkey for the user
  /// This is called after successful account creation
  Future<void> registerPasskey({
    required String userEmail,
    required String userName,
    required String walletAddress,
  }) async {
    try {
      emit(state.copyWith(status: PasskeyAuthStatus.registering));

      final result = await _passkeyAuthService.registerPasskey(
        userEmail: userEmail,
        userName: userName,
        walletAddress: walletAddress,
      );

      if (result.isSuccess) {
        // Update authentication state with new token
        if (result.accessToken != null) {
          await SharedPref.setBool(KeyShared.isLogin, true);
          await SharedPref.setString(KeyShared.tokenKey, result.accessToken!);
        }

        emit(state.copyWith(
          status: PasskeyAuthStatus.registered,
          userEmail: userEmail,
          credentialId: result.credentialId,
        ));
      } else if (result.isNotSupported) {
        emit(state.copyWith(
          status: PasskeyAuthStatus.notSupported,
          isSupported: false,
        ));
      } else {
        emit(state.copyWith(
          status: PasskeyAuthStatus.failure,
          errorMessage: result.error ?? 'Registration failed',
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        status: PasskeyAuthStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  /// Check if user has existing passkey credentials
  Future<void> checkExistingCredentials() async {
    try {
      final hasCredentials = await _passkeyAuthService.hasExistingPasskey();
      
      emit(state.copyWith(
        hasExistingCredentials: hasCredentials,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: PasskeyAuthStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  /// Clear passkey credentials (for logout)
  Future<void> clearPasskeyCredentials() async {
    try {
      await _passkeyAuthService.clearPasskeyCredentials();
      
      emit(state.copyWith(
        status: PasskeyAuthStatus.initial,
        userEmail: null,
        credentialId: null,
        hasExistingCredentials: false,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: PasskeyAuthStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  /// Reset state to initial
  void resetState() {
    emit(const PasskeyAuthState());
  }

  /// Skip passkey authentication and proceed with normal flow
  void skipPasskeyAuth() {
    emit(state.copyWith(status: PasskeyAuthStatus.skipped));
  }
}
